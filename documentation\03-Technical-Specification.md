# Technical Specification
## LumusAI - Intelligent Document Processing Service

**Document Version:** 1.0  
**Date:** December 2024  
**Technical Lead:** Development Team  
**Project:** LumusAI  

---

## Table of Contents

1. [Technology Stack](#1-technology-stack)
2. [Development Environment](#2-development-environment)
3. [API Specifications](#3-api-specifications)
4. [Data Models](#4-data-models)
5. [Processing Algorithms](#5-processing-algorithms)
6. [<PERSON><PERSON><PERSON> Handling](#6-error-handling)
7. [Performance Specifications](#7-performance-specifications)
8. [Security Specifications](#8-security-specifications)

---

## 1. Technology Stack

### 1.1 Core Technologies

**Backend Framework:**
- **FastAPI 0.115.2:** Modern, fast web framework for building APIs
- **Python 3.12.7:** Programming language and runtime
- **Uvicorn 0.32.0:** ASGI server for production deployment

**AI/ML Integration:**
- **LangChain 0.3.19:** Framework for LLM application development
- **OpenAI 1.58.1:** OpenAI API client library
- **LangChain-OpenAI 0.3.6:** OpenAI integration for LangChain

**Data Processing:**
- **Pydantic 2.9.2:** Data validation and serialization
- **PyMuPDF 1.24.13:** PDF processing and text extraction
- **python-docx 1.1.2:** Microsoft Word document processing
- **OpenPyXL 3.1.5:** Excel file processing
- **Pillow 11.0.0:** Image processing capabilities

**Utilities:**
- **Pandas 2.2.3:** Data manipulation and analysis
- **Requests 2.32.3:** HTTP client library
- **python-dotenv 1.0.1:** Environment variable management
- **psutil 6.1.1:** System and process monitoring

### 1.2 Development Tools

**Testing:**
- **pytest 8.3.4:** Testing framework
- **pytest plugins:** Extended testing capabilities

**Code Quality:**
- **Type hints:** Python typing for better code quality
- **Pydantic validation:** Runtime data validation
- **FastAPI automatic validation:** Request/response validation

**Documentation:**
- **OpenAPI/Swagger:** Automatic API documentation
- **Markdown:** Documentation format

### 1.3 Infrastructure

**Containerization:**
- **Docker:** Application containerization
- **Alpine Linux:** Lightweight base image

**Monitoring:**
- **Built-in health checks:** System monitoring endpoints
- **Structured logging:** Comprehensive logging system
- **Performance metrics:** Request timing and resource usage

## 2. Development Environment

### 2.1 Prerequisites

**System Requirements:**
- Python 3.12.7 or higher
- Docker and Docker Compose
- Git version control
- 4GB RAM minimum, 8GB recommended
- 2GB available disk space

**External Dependencies:**
- OpenAI API access or Azure OpenAI Service
- Internet connectivity for AI model access
- Valid API credentials and quotas

### 2.2 Local Development Setup

**Environment Configuration:**
```bash
# Clone repository
git clone <repository-url>
cd LumusAI

# Create virtual environment
python -m venv venv
source venv/bin/activate  # Linux/Mac
# or
venv\Scripts\activate     # Windows

# Install dependencies
pip install -r requirements.txt

# Configure environment
cp .env.example .env
# Edit .env with your configuration
```

**Required Environment Variables:**
```env
API_KEY=your_openai_api_key
API_VERSION=2023-05-15
AZURE_ENDPOINT=https://your-resource.openai.azure.com/
MODEL=gpt-4-vision-preview
MAX_CONCURRENT_TASKS=4
```

### 2.3 Docker Development

**Docker Compose Setup:**
```yaml
version: '3.8'
services:
  lumusai:
    build: .
    ports:
      - "8000:8000"
    environment:
      - API_KEY=${API_KEY}
      - API_VERSION=${API_VERSION}
      - AZURE_ENDPOINT=${AZURE_ENDPOINT}
      - MODEL=${MODEL}
    volumes:
      - ./logs:/app/logs
```

## 3. API Specifications

### 3.1 Core Endpoints

#### 3.1.1 Document Processing Endpoint

**Endpoint:** `POST /process`

**Request Format:**
```http
POST /process
Content-Type: multipart/form-data

Parameters:
- action: string (required) - Document type identifier
- file: file (optional) - Document file upload
- data: string (optional) - URL or text data
```

**Supported Actions:**
- `cv` - CV/Resume processing
- `invoice` - Invoice processing
- `tutela_contestacion` - Legal contestation documents
- `tutela_fallo` - Legal ruling documents
- `tutela_desacato` - Legal contempt documents
- `tutela_correo` - Legal email communications

**Response Format:**
```json
{
  "status": "success",
  "data": {
    // Structured data based on document type
  },
  "metadata": {
    "processing_time": 45.2,
    "token_usage": {
      "prompt_tokens": 1500,
      "completion_tokens": 800,
      "total_tokens": 2300,
      "cost": 0.046
    }
  }
}
```

#### 3.1.2 Health Monitoring Endpoint

**Endpoint:** `GET /health`

**Response Format:**
```json
{
  "status": "ok",
  "version": "*********.12",
  "message": "Service is running",
  "system_metrics": {
    "cpu_usage_percent": 25.4,
    "memory_usage_percent": 68.2
  },
  "tasks": {
    "processing_count": 2,
    "processing_details": [
      {
        "task_id": "task_123",
        "action": "cv",
        "running_time_seconds": 12.5,
        "file_name": "resume.pdf"
      }
    ],
    "waiting_count": 1
  }
}
```

#### 3.1.3 Maintenance Endpoint

**Endpoint:** `GET /maintenance/tasks`

**Purpose:** Task cleanup and maintenance operations

### 3.2 Error Response Format

**Standard Error Response:**
```json
{
  "status": "error",
  "error": {
    "code": "VALIDATION_ERROR",
    "message": "Invalid document format",
    "details": "Supported formats: PDF, DOCX, XLS, XLSX"
  },
  "timestamp": "2024-12-01T10:30:00Z"
}
```

**HTTP Status Codes:**
- `200` - Success
- `400` - Bad Request (validation errors)
- `422` - Unprocessable Entity (model validation)
- `500` - Internal Server Error
- `503` - Service Unavailable (overloaded)

## 4. Data Models

### 4.1 CV Processing Models

**Personal Information:**
```python
class PersonalInfo(BaseModel):
    name: Optional[str] = Field(None, description="Full name")
    email: Optional[str] = Field(None, description="Email address(es), separated by semicolons if multiple")
    phone: Optional[str] = Field(None, description="Phone number")
    location: Optional[str] = Field(None, description="Location/address")
    linkedin: Optional[str] = Field(None, description="LinkedIn profile")
```

**Work Experience:**
```python
class WorkExperience(BaseModel):
    company: Optional[str] = Field(None, description="Company name")
    position: Optional[str] = Field(None, description="Job title")
    start_date: Optional[str] = Field(None, description="Start date")
    end_date: Optional[str] = Field(None, description="End date")
    duration_months: Optional[int] = Field(None, description="Duration in months")
    responsibilities: Optional[List[str]] = Field(None, description="Job responsibilities")
    technologies: Optional[List[str]] = Field(None, description="Technologies used")
```

### 4.2 Invoice Processing Models

**Invoice Base:**
```python
class InvoiceBase(BaseModel):
    invoice_number: str = Field(..., description="Invoice number")
    invoice_type: str = Field(..., description="Invoice type")
    description: str = Field(..., description="Invoice description")
    invoice_date: Optional[str] = Field(None, description="Invoice date")
    due_date: Optional[str] = Field(None, description="Due date")
```

**Product Item:**
```python
class ProductItem(BaseModel):
    description: str = Field(..., description="Product/service description")
    quantity: float = Field(0.0, description="Quantity")
    unit_price: float = Field(0.0, description="Unit price")
    total_price: float = Field(0.0, description="Total price")
```

### 4.3 Legal Document Models

**Tutela Document:**
```python
class TutelaContestacionDocument(BaseModel):
    case_number: Optional[str] = Field(None, description="Case number")
    plaintiff: Optional[str] = Field(None, description="Plaintiff name")
    defendant: Optional[str] = Field(None, description="Defendant name")
    court: Optional[str] = Field(None, description="Court name")
    date: Optional[str] = Field(None, description="Document date")
    summary: Optional[str] = Field(None, description="Document summary")
```

## 5. Processing Algorithms

### 5.1 Document Processing Pipeline

**Step 1: Input Validation**
```python
def validate_input(file: UploadFile, data: str) -> bool:
    # Validate file format and size
    # Check for required parameters
    # Sanitize input data
    pass
```

**Step 2: Text Extraction**
```python
def extract_text(file_path: str, file_type: str) -> str:
    if file_type == 'pdf':
        return extract_pdf_text(file_path)
    elif file_type == 'docx':
        return extract_docx_text(file_path)
    # Additional format handlers
```

**Step 3: AI Processing**
```python
async def process_with_ai(text: str, model_class: BaseModel) -> dict:
    # Prepare prompt with text and model schema
    # Send to OpenAI/Azure OpenAI
    # Parse and validate response
    # Return structured data
```

### 5.2 Concurrency Management

**Semaphore-based Limiting:**
```python
semaphore = asyncio.Semaphore(MAX_CONCURRENT_TASKS)

async def process_document(processor, file, data):
    async with semaphore:
        return await processor.process(file, data)
```

**Task Tracking:**
```python
active_tasks = {}

def register_task(task, metadata):
    active_tasks[task] = {
        "start_time": time.time(),
        "action": metadata["action"],
        "file_name": metadata.get("file_name")
    }
```

### 5.3 Error Recovery

**Retry Logic:**
```python
async def retry_with_backoff(func, max_retries=3):
    for attempt in range(max_retries):
        try:
            return await func()
        except RetryableError as e:
            if attempt == max_retries - 1:
                raise
            await asyncio.sleep(2 ** attempt)
```

## 6. Error Handling

### 6.1 Error Categories

**Validation Errors:**
- Invalid file formats
- Missing required parameters
- Data validation failures

**Processing Errors:**
- AI model API failures
- Document parsing errors
- Timeout errors

**System Errors:**
- Resource exhaustion
- Network connectivity issues
- Configuration errors

### 6.2 Error Response Strategy

**Client Errors (4xx):**
- Return descriptive error messages
- Include validation details
- Suggest corrective actions

**Server Errors (5xx):**
- Log detailed error information
- Return generic error messages
- Implement automatic retry where appropriate

### 6.3 Logging Strategy

**Log Levels:**
- `ERROR`: System errors and failures
- `WARNING`: Recoverable issues and retries
- `INFO`: Request processing and performance
- `DEBUG`: Detailed debugging information

**Log Format:**
```json
{
  "timestamp": "2024-12-01T10:30:00Z",
  "level": "INFO",
  "message": "Document processed successfully",
  "request_id": "req_123",
  "processing_time": 45.2,
  "action": "cv",
  "file_name": "resume.pdf"
}
```

## 7. Performance Specifications

### 7.1 Response Time Requirements

**Target Performance:**
- Document processing: < 60 seconds (95th percentile)
- Health checks: < 2 seconds
- API acknowledgment: < 5 seconds

**Performance Monitoring:**
- Request timing middleware
- Token usage tracking
- Resource utilization monitoring

### 7.2 Throughput Requirements

**Concurrent Processing:**
- Default: 4 simultaneous tasks
- Configurable via environment variable
- Queue management for excess requests

**Capacity Planning:**
- 100+ documents per hour
- Support for burst traffic
- Graceful degradation under load

### 7.3 Resource Optimization

**Memory Management:**
- Temporary file cleanup
- Garbage collection optimization
- Memory usage monitoring

**CPU Optimization:**
- Asynchronous processing
- Efficient text processing
- Optimized AI model calls

## 8. Security Specifications

### 8.1 Input Security

**File Validation:**
- File type verification
- Size limit enforcement (50MB)
- Content sanitization

**Data Validation:**
- Pydantic model validation
- Input sanitization
- SQL injection prevention

### 8.2 API Security

**Authentication:**
- API key management for external services
- Configurable authentication (future enhancement)
- Request rate limiting (future enhancement)

**Data Protection:**
- Secure temporary file handling
- Memory cleanup after processing
- No persistent data storage

### 8.3 External Service Security

**OpenAI Integration:**
- Secure API key storage
- HTTPS communication
- Request/response validation

**Network Security:**
- CORS configuration
- Request size limits
- Timeout configurations

---

**Document Control:**
- **Version:** 1.0
- **Status:** Final
- **Last Updated:** December 2024
- **Next Review:** Upon technical changes
